# Monitor 系统架构文档

## 1. 系统概述

本服务是一个基于Spring Boot的系统监控平台，通过WebSocket实时推送系统监控数据，并集成了Kafka消息队列和Redis缓存，用于收集和展示系统性能指标。

## 2. 技术栈

- 后端框架：Spring Boot 3.0.7
- 消息队列：Kafka
- 缓存：Redis
- 实时通信：WebSocket
- 监控指标：Prometheus
- Java版本：JDK 17

## 3. 系统架构

![monitor架构图.png](monitor架构图.png)


## 4. 核心模块

### 4.1 WebSocket模块
- MonitorWebSocketController：处理WebSocket连接和消息
- WebsocketUtil：管理WebSocket会话和消息推送
- WebSocketConfig：WebSocket配置类

### 4.2 监控服务模块
- MonitorService：监控服务接口
- MonitorServiceImpl：实现系统信息采集和监控数据处理
- IndexData：系统指标数据实体
- CurrentProcessInformation：进程信息实体

### 4.3 消息处理模块
- Listener：Kafka消息监听器，处理消息队列数据
- Redis集成：使用Redisson客户端进行缓存管理

## 5. 数据流

1. 系统通过Kafka接收外部系统日志和监控数据
2. Listener组件处理Kafka消息并存储到Redis
3. MonitorService采集系统信息和Prometheus指标
4. 通过WebSocket实时推送监控数据到客户端

## 6. 项目结构

```
src/main/java/com/geeksec/monitor/
├── config/          # 配置类
├── controller/      # WebSocket控制器
├── entity/          # 数据实体
├── kafka/           # Kafka消息处理
├── service/         # 业务服务
│   └── impl/       # 服务实现
└── utils/           # 工具类
```