package com.geeksec.graph.application.domain.dto.response;

import com.geeksec.graph.application.label.dto.LabelDto;

import java.util.List;

/**
 * 域名响应DTO
 *
 * <AUTHOR>
 */
public record DomainResponseDto(
    /**
     * 域名ID
     */
    String id,

    /**
     * 域名地址
     */
    String domainAddr,

    /**
     * 流量字节数
     */
    Long bytes,

    /**
     * 平均流量速率
     */
    Long averageBps,

    /**
     * 域名排名
     */
    Long domainRank,

    /**
     * 流行度等级
     */
    Integer popularityLevel,

    /**
     * Whois信息
     */
    String whois,

    /**
     * 首次发现时间
     */
    Integer firstSeen,

    /**
     * 最后发现时间
     */
    Integer lastSeen,

    /**
     * 是否在黑名单中
     */
    Integer blackList,

    /**
     * 备注
     */
    String remark,

    /**
     * 标签列表
     */
    List<LabelDto> labels
) {
    /**
     * 创建DomainResponseDto的Builder
     *
     * @return Builder实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DomainResponseDto的Builder类
     */
    public static class Builder {
        private String id;
        private String domainAddr;
        private Long bytes;
        private Long averageBps;
        private Long domainRank;
        private Integer popularityLevel;
        private String whois;
        private Integer firstSeen;
        private Integer lastSeen;
        private Integer blackList;
        private String remark;
        private List<LabelDto> labels;

        public Builder id(String id) {
            this.id = id;
            return this;
        }

        public Builder domainAddr(String domainAddr) {
            this.domainAddr = domainAddr;
            return this;
        }

        public Builder bytes(Long bytes) {
            this.bytes = bytes;
            return this;
        }

        public Builder averageBps(Long averageBps) {
            this.averageBps = averageBps;
            return this;
        }

        public Builder domainRank(Long domainRank) {
            this.domainRank = domainRank;
            return this;
        }

        public Builder popularityLevel(Integer popularityLevel) {
            this.popularityLevel = popularityLevel;
            return this;
        }

        public Builder whois(String whois) {
            this.whois = whois;
            return this;
        }

        public Builder firstSeen(Integer firstSeen) {
            this.firstSeen = firstSeen;
            return this;
        }

        public Builder lastSeen(Integer lastSeen) {
            this.lastSeen = lastSeen;
            return this;
        }

        public Builder blackList(Integer blackList) {
            this.blackList = blackList;
            return this;
        }

        public Builder remark(String remark) {
            this.remark = remark;
            return this;
        }

        public Builder labels(List<LabelDto> labels) {
            this.labels = labels;
            return this;
        }

        public DomainResponseDto build() {
            return new DomainResponseDto(id, domainAddr, bytes, averageBps, domainRank, popularityLevel, whois, firstSeen, lastSeen, blackList, remark, labels);
        }
    }
}
