# Alexa排名到Tranco排名迁移指南

## 概述

由于Alexa排名服务已于2022年5月1日正式停服，本项目已完成从Alexa排名到Tranco排名的迁移。Tranco是一个研究导向的域名排名列表，整合了多个数据源，具有更好的抗操纵性和研究价值。

## 迁移内容

### 1. 核心组件

#### TrancoRankingManager
- **位置**: `flink-jobs/shared-core/src/main/java/com/geeksec/common/utils/metadata/TrancoRankingManager.java`
- **功能**: 管理Tranco排名数据，提供域名排名查询和流行度等级评估
- **特性**: 单例模式、内存缓存、支持数据刷新

#### DomainDimensionTableFunction
- **位置**: `flink-jobs/data-warehouse-processor/src/main/java/com/geeksec/nta/datawarehouse/etl/dim/function/DomainDimensionTableFunction.java`
- **更新**: 集成TrancoRankingManager，使用真实排名数据替代硬编码值

#### DomainMetadataManager
- **位置**: `flink-jobs/shared-core/src/main/java/com/geeksec/common/utils/metadata/DomainMetadataManager.java`
- **更新**: 移除Alexa相关方法，添加Tranco排名支持

### 2. 数据模型更新

#### 字段变更
- `alexa_rank` → `domain_rank`: 域名排名字段
- 新增 `popularity_level`: 流行度等级字段(1-5)

#### 流行度等级定义
- **等级1（极高）**: 排名1-1000
- **等级2（高）**: 排名1001-10000  
- **等级3（中等）**: 排名10001-100000
- **等级4（低）**: 排名100001-500000
- **等级5（极低）**: 排名500000+
- **等级0（未知）**: 不在排名中

### 3. 数据源

#### Tranco数据文件
- **位置**: `flink-jobs/data-warehouse-processor/src/main/resources/top-1m.csv`
- **格式**: CSV格式，包含100万条域名排名数据
- **结构**: `排名,域名`

## 数据库迁移

### 执行迁移脚本
```sql
-- 执行迁移脚本
source deployment/helm/files/sql/migration/migrate_alexa_to_tranco.sql
```

### 手动迁移步骤
1. 为`dim_domain`表添加新字段
2. 可选：迁移历史数据
3. 更新Nebula图数据库标签结构
4. 创建索引提高查询性能

## 代码更新

### 影响的文件
- `DomainVertex.java`: 更新字段名
- `DomainVertexConverter.java`: 更新Row结构
- `DomainResponseDto.java`: 更新API响应结构
- `01-dim-tables.sql`: 更新表结构定义

### API变更
```java
// 旧API（已移除）
Integer alexaRank = domainMetadataManager.getDomainAlexaRank(domain);

// 新API
Integer domainRank = domainMetadataManager.getDomainRank(domain);
Integer popularityLevel = domainMetadataManager.getDomainPopularityLevel(domain);
boolean isRanked = domainMetadataManager.isRankedDomain(domain);
```

## 测试验证

### 单元测试
- **位置**: `flink-jobs/data-warehouse-processor/src/test/java/com/geeksec/nta/datawarehouse/etl/dim/function/TrancoRankingTest.java`
- **覆盖**: 排名查询、流行度等级、边界条件测试

### 演示程序
- **位置**: `flink-jobs/data-warehouse-processor/src/main/java/com/geeksec/nta/datawarehouse/demo/TrancoRankingDemo.java`
- **功能**: 验证Tranco排名管理器的基本功能

## 部署注意事项

### 1. 数据文件
确保Tranco数据文件`top-1m.csv`已正确放置在resources目录中。

### 2. 数据库更新
执行迁移脚本前请备份相关数据表。

### 3. 前端更新
更新前端显示文本，将"Alexa排名"改为"域名排名"。

### 4. 监控验证
部署后监控系统日志，确认Tranco排名数据正常加载和使用。

## 性能优化

### 内存使用
- Tranco数据全量加载到内存，约占用50-100MB
- 使用ConcurrentHashMap提供线程安全的快速查询

### 查询性能
- O(1)时间复杂度的域名排名查询
- 支持数据刷新，无需重启应用

## 故障排除

### 常见问题
1. **数据文件未找到**: 检查`top-1m.csv`文件路径
2. **内存不足**: 调整JVM堆内存大小
3. **排名数据为0**: 确认域名格式正确，检查数据文件完整性

### 日志监控
关注以下日志信息：
- Tranco排名数据加载成功/失败
- 域名查询异常
- 内存使用情况

## 后续计划

1. **数据更新机制**: 建立定期更新Tranco数据的机制
2. **多源融合**: 考虑整合其他域名信誉数据源
3. **性能优化**: 根据实际使用情况优化内存和查询性能
4. **监控告警**: 建立数据质量监控和异常告警机制

## 联系信息

如有问题请联系：
- 开发团队：<EMAIL>
- 项目仓库：http://gitlab.gs.lan:8888/dev-team/nta.git
