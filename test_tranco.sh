#!/bin/bash

# 简单测试脚本，验证Tranco数据文件是否正确加载

echo "=== Tranco数据文件验证 ==="

# 检查文件是否存在
TRANCO_FILE="flink-jobs/data-warehouse-processor/src/main/resources/top-1m.csv"

if [ ! -f "$TRANCO_FILE" ]; then
    echo "错误: Tranco数据文件不存在: $TRANCO_FILE"
    exit 1
fi

echo "✓ Tranco数据文件存在: $TRANCO_FILE"

# 检查文件大小
FILE_SIZE=$(wc -l < "$TRANCO_FILE")
echo "✓ 文件行数: $FILE_SIZE"

# 检查前10行数据格式
echo "✓ 前10行数据预览:"
head -10 "$TRANCO_FILE"

echo ""
echo "=== 测试特定域名排名 ==="

# 测试一些知名域名的排名
test_domains=("google.com" "microsoft.com" "facebook.com" "apple.com" "baidu.com")

for domain in "${test_domains[@]}"; do
    rank=$(grep ",$domain$" "$TRANCO_FILE" | cut -d',' -f1)
    if [ -n "$rank" ]; then
        echo "✓ $domain: 排名 $rank"
    else
        echo "✗ $domain: 未找到排名"
    fi
done

echo ""
echo "=== 数据统计 ==="
echo "总域名数量: $(wc -l < "$TRANCO_FILE")"
echo "前1000名域名数量: $(head -1000 "$TRANCO_FILE" | wc -l)"
echo "包含'google'的域名数量: $(grep -i google "$TRANCO_FILE" | wc -l)"

echo ""
echo "=== 验证完成 ==="
