package com.geeksec.nta.datawarehouse.demo;

import com.geeksec.common.utils.metadata.TrancoRankingManager;

/**
 * Tranco排名演示程序
 * 用于验证Tranco排名管理器的功能
 *
 * <AUTHOR>
 */
public class TrancoRankingDemo {

    public static void main(String[] args) {
        System.out.println("=== Tranco域名排名演示 ===");
        
        TrancoRankingManager manager = TrancoRankingManager.getInstance();
        
        // 测试一些知名域名
        String[] testDomains = {
            "google.com",
            "microsoft.com", 
            "facebook.com",
            "apple.com",
            "amazon.com",
            "youtube.com",
            "baidu.com",
            "unknown-domain-12345.com"
        };
        
        System.out.println("总排名数据量: " + manager.getTotalRankingCount());
        System.out.println();
        
        for (String domain : testDomains) {
            int rank = manager.getDomainRank(domain);
            int level = manager.getDomainPopularityLevel(domain);
            boolean isRanked = manager.isRankedDomain(domain);
            
            System.out.printf("域名: %-25s | 排名: %-8d | 流行度等级: %d | 是否在排名中: %s%n", 
                domain, rank, level, isRanked ? "是" : "否");
        }
        
        System.out.println();
        System.out.println("流行度等级说明:");
        System.out.println("1 - 极高流行度 (排名 1-1000)");
        System.out.println("2 - 高流行度 (排名 1001-10000)");
        System.out.println("3 - 中等流行度 (排名 10001-100000)");
        System.out.println("4 - 低流行度 (排名 100001-500000)");
        System.out.println("5 - 极低流行度 (排名 500001+)");
        System.out.println("0 - 未知 (不在排名中)");
    }
}
