package com.geeksec.nta.datawarehouse.etl.dim.function;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import org.apache.commons.lang3.StringUtils;
import com.geeksec.common.utils.time.DateTimeUtils;
import com.geeksec.common.utils.metadata.TrancoRankingManager;

/**
 * 域名维度表处理函数
 * 专门用于生成符合dim_domain表结构的维度数据
 * 使用Flink Backend State管理域名信息缓存
 *
 * <AUTHOR>
 */
@Slf4j
public class DomainDimensionTableFunction extends ProcessFunction<Row, Row> {

    /** 域名部分最小长度 */
    private static final int MIN_DOMAIN_PARTS = 2;

    /** 域名维度侧输出标签 */
    public static final OutputTag<Row> DOMAIN_DIM_TAG = new OutputTag<Row>("DomainDimension") {};

    /** 域名信息缓存状态，使用MapState管理 */
    private transient MapState<String, Map<String, Object>> domainInfoCache;

    /** 缓存TTL配置，默认24小时 */
    private final Duration cacheTtl;

    /** 域名字段名 */
    private final String domainFieldName;

    /** Tranco排名管理器 */
    private transient TrancoRankingManager trancoRankingManager;

    /**
     * 构造函数
     *
     * @param domainFieldName 域名字段名
     */
    public DomainDimensionTableFunction(String domainFieldName) {
        this(domainFieldName, Duration.ofHours(24));
    }

    /**
     * 构造函数
     *
     * @param domainFieldName 域名字段名
     * @param cacheTtl 缓存TTL时间
     */
    public DomainDimensionTableFunction(String domainFieldName, Duration cacheTtl) {
        this.domainFieldName = domainFieldName;
        this.cacheTtl = cacheTtl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(cacheTtl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 创建MapState描述符
        MapStateDescriptor<String, Map<String, Object>> descriptor = new MapStateDescriptor<>(
                "domain-dimension-cache",
                TypeInformation.of(new TypeHint<String>() {}),
                TypeInformation.of(new TypeHint<Map<String, Object>>() {})
        );

        // 启用TTL
        descriptor.enableTimeToLive(ttlConfig);

        // 获取状态
        domainInfoCache = getRuntimeContext().getMapState(descriptor);

        // 初始化Tranco排名管理器
        trancoRankingManager = TrancoRankingManager.getInstance();
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        // 获取域名
        String domain = getDomainFromRow(value);

        if (StringUtils.isEmpty(domain)) {
            // 域名无效，直接输出原始数据
            out.collect(value);
            return;
        }

        // 获取丰富化的域名信息
        Map<String, Object> enrichedInfo = getEnrichedDomainInfo(domain);

        // 创建维度表记录
        Row dimensionRow = createDimensionRow(domain, enrichedInfo);

        // 输出到域名维度侧输出流
        ctx.output(DOMAIN_DIM_TAG, dimensionRow);

        // 输出原始数据
        out.collect(value);
    }

    /**
     * 从Row中获取域名
     *
     * @param row 输入Row
     * @return 域名字符串
     */
    private String getDomainFromRow(Row row) {
        try {
            Object domainValue = row.getField(domainFieldName);
            return domainValue != null ? domainValue.toString() : null;
        } catch (Exception e) {
            log.warn("无法从Row中获取域名字段 {}: {}", domainFieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 获取丰富化的域名信息，优先从缓存获取
     *
     * @param domain 域名
     * @return 丰富化的域名信息
     * @throws Exception 状态访问异常
     */
    private Map<String, Object> getEnrichedDomainInfo(String domain) throws Exception {
        // 检查缓存
        Map<String, Object> cachedInfo = domainInfoCache.get(domain);
        if (cachedInfo != null) {
            return cachedInfo;
        }

        // 缓存未命中，重新计算并缓存
        Map<String, Object> enrichedInfo = enrichDomainInfo(domain);
        domainInfoCache.put(domain, enrichedInfo);

        return enrichedInfo;
    }

    /**
     * 丰富域名信息
     *
     * @param domain 域名
     * @return 丰富后的域名信息Map
     */
    private Map<String, Object> enrichDomainInfo(String domain) {
        Map<String, Object> domainInfo = new HashMap<>(8);
        domainInfo.put("domain", domain);
        domainInfo.put("is_valid", true);

        try {
            // 提取基础域名
            String baseDomain = extractBaseDomain(domain);
            domainInfo.put("base_domain", baseDomain);

            // 获取Tranco排名
            int domainRank = trancoRankingManager.getDomainRank(domain);
            domainInfo.put("domain_rank", domainRank);

            // TODO: 实现域名WHOIS信息查询逻辑
            domainInfo.put("whois", "");

        } catch (Exception e) {
            log.warn("获取域名信息失败: {}", domain, e);
        }

        return domainInfo;
    }

    /**
     * 提取基础域名
     * 例如从www.example.com提取example.com
     *
     * @param domain 完整域名
     * @return 基础域名
     */
    private String extractBaseDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return "";
        }

        // 简单实现，仅作示例
        // 实际应用中应使用更复杂的算法，考虑各国顶级域名规则
        String[] parts = domain.split("\\.");
        if (parts.length <= MIN_DOMAIN_PARTS) {
            return domain;
        }

        return parts[parts.length - 2] + "." + parts[parts.length - 1];
    }

    /**
     * 创建符合维度表结构的域名维度记录
     *
     * @param domain 域名
     * @param enrichmentInfo 丰富化信息
     * @return 符合dim_domain表结构的Row
     */
    private Row createDimensionRow(String domain, Map<String, Object> enrichmentInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("domain", domain);

        // 设置域名相关信息字段
        dimensionRow.setField("alexa_rank", enrichmentInfo.get("alexa_rank"));
        dimensionRow.setField("whois", enrichmentInfo.get("whois"));

        // 设置默认值字段
        dimensionRow.setField("threat_score", null);
        dimensionRow.setField("trust_score", null);
        dimensionRow.setField("remark", null);

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", 0L);
        dimensionRow.setField("total_queries", 1L);

        // 设置HLL字段（这里暂时设置为null，实际使用时需要根据具体需求设置）
        dimensionRow.setField("unique_client_ips", null);

        // 设置时间字段
        DateTimeFormatter formatter = DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER != null
                ? DateTimeUtils.DEFAULT_DATE_TIME_FORMATTER
                : DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = LocalDateTime.now().format(formatter);
        dimensionRow.setField("first_seen", currentTime);
        dimensionRow.setField("last_seen", currentTime);
        dimensionRow.setField("create_time", currentTime);
        dimensionRow.setField("update_time", currentTime);

        return dimensionRow;
    }
}
