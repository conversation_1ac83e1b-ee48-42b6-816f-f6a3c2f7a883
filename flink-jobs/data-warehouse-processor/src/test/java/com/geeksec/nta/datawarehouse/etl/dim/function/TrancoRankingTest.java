package com.geeksec.nta.datawarehouse.etl.dim.function;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import com.geeksec.common.utils.metadata.TrancoRankingManager;

/**
 * Tranco排名管理器测试类
 * 测试基于Tranco数据的域名排名功能
 *
 * <AUTHOR>
 */
public class TrancoRankingTest {

    private TrancoRankingManager trancoRankingManager;

    @BeforeEach
    public void setUp() {
        trancoRankingManager = TrancoRankingManager.getInstance();
    }

    @Test
    public void testGetDomainRank() {
        // 测试排名第一的域名
        int googleRank = trancoRankingManager.getDomainRank("google.com");
        assertEquals(1, googleRank, "google.com应该排名第一");

        // 测试其他知名域名
        int microsoftRank = trancoRankingManager.getDomainRank("microsoft.com");
        assertTrue(microsoftRank > 0 && microsoftRank <= 10, "microsoft.com应该在前10名");

        // 测试不存在的域名
        int unknownRank = trancoRankingManager.getDomainRank("unknown-domain-12345.com");
        assertEquals(0, unknownRank, "不存在的域名应该返回0");
    }

    @Test
    public void testIsRankedDomain() {
        // 测试已排名的域名
        assertTrue(trancoRankingManager.isRankedDomain("google.com"), "google.com应该在排名中");
        assertTrue(trancoRankingManager.isRankedDomain("microsoft.com"), "microsoft.com应该在排名中");

        // 测试未排名的域名
        assertFalse(trancoRankingManager.isRankedDomain("unknown-domain-12345.com"), "不存在的域名不应该在排名中");
    }

    @Test
    public void testGetDomainPopularityLevel() {
        // 测试极高流行度域名（排名1-1000）
        int googleLevel = trancoRankingManager.getDomainPopularityLevel("google.com");
        assertEquals(1, googleLevel, "google.com应该是极高流行度");

        // 测试未知域名
        int unknownLevel = trancoRankingManager.getDomainPopularityLevel("unknown-domain-12345.com");
        assertEquals(0, unknownLevel, "不存在的域名应该返回未知等级");
    }

    @Test
    public void testGetTotalRankingCount() {
        int totalCount = trancoRankingManager.getTotalRankingCount();
        assertTrue(totalCount > 0, "排名数据总数应该大于0");
        System.out.println("总排名数据量: " + totalCount);
    }

    @Test
    public void testNullAndEmptyDomain() {
        // 测试null域名
        assertEquals(0, trancoRankingManager.getDomainRank(null), "null域名应该返回0");
        assertFalse(trancoRankingManager.isRankedDomain(null), "null域名不应该在排名中");

        // 测试空字符串域名
        assertEquals(0, trancoRankingManager.getDomainRank(""), "空域名应该返回0");
        assertFalse(trancoRankingManager.isRankedDomain(""), "空域名不应该在排名中");

        // 测试空白字符串域名
        assertEquals(0, trancoRankingManager.getDomainRank("   "), "空白域名应该返回0");
        assertFalse(trancoRankingManager.isRankedDomain("   "), "空白域名不应该在排名中");
    }
}
