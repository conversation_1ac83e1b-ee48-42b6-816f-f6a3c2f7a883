package com.geeksec.nta.graphbuilder.model.vertex;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 域名顶点
 * 表示域名信息
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DomainVertex extends BaseVertex {
    /**
     * 域名地址
     */
    private String domainAddr;

    /**
     * 威胁分数
     */
    private Integer threatScore;

    /**
     * 信任分数
     */
    private Integer trustScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 域名排名
     */
    private Integer domainRank;

    /**
     * 流行度等级
     */
    private Integer popularityLevel;

    /**
     * Whois信息
     */
    private String whois;

    @Override
    public String getVertexId() {
        return domainAddr;
    }

    /**
     * 获取顶点标签
     *
     * @return 顶点标签枚举
     */
    @Override
    public VertexTag getVertexTag() {
        return VertexTag.DOMAIN;
    }
}
