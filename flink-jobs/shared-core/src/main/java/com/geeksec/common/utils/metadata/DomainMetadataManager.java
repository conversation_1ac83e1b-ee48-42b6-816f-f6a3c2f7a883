package com.geeksec.common.utils.metadata;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;

import com.geeksec.common.utils.db.DatabaseConnectionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 域名元数据管理器
 * 提供域名相关元数据的获取和缓存功能
 * 支持Alexa排名（已停服）和Tranco排名数据
 *
 * <AUTHOR>
 */
@Slf4j
public class DomainMetadataManager {

    /**
     * 单例实例
     */
    private static volatile DomainMetadataManager instance = null;

    /**
     * 域名WHOIS信息映射
     */
    private final Map<String, String> domainWhoisMap = new ConcurrentHashMap<>();

    /**
     * 域名Alexa排名映射（已停服，保留用于向后兼容）
     */
    private final Map<String, Integer> domainAlexaMap = new ConcurrentHashMap<>();

    /**
     * Tranco排名管理器实例
     */
    private TrancoRankingManager trancoRankingManager;

    /**
     * 私有构造函数
     */
    private DomainMetadataManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return DomainMetadataManager实例
     */
    public static DomainMetadataManager getInstance() {
        if (instance == null) {
            synchronized (DomainMetadataManager.class) {
                if (instance == null) {
                    instance = new DomainMetadataManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化元数据
     */
    private void initialize() {
        try {
            // 初始化Tranco排名管理器
            trancoRankingManager = TrancoRankingManager.getInstance();
            // 初始化时不加载数据，等到需要时再加载
            log.info("域名元数据管理器初始化成功");
        } catch (Exception e) {
            log.error("初始化域名元数据管理器失败", e);
        }
    }

    /**
     * 获取域名WHOIS信息
     *
     * @param domain 域名
     * @return WHOIS信息，如果不存在则返回空字符串
     */
    public String getDomainWhois(String domain) {
        // 如果缓存为空，加载域名WHOIS信息
        if (domainWhoisMap.isEmpty()) {
            loadDomainWhoisInfo();
        }

        return domainWhoisMap.getOrDefault(domain, StringUtils.EMPTY);
    }

    /**
     * 加载域名WHOIS信息
     */
    private void loadDomainWhoisInfo() {
        domainWhoisMap.clear();

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement()) {

            String sql = "SELECT * FROM tb_domain_whois LIMIT 1000000";

            try (ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    domainWhoisMap.put(rs.getString(1), rs.getString(2));
                }
            }

            log.info("域名WHOIS信息加载成功，共加载 {} 条记录", domainWhoisMap.size());
        } catch (Exception e) {
            log.error("加载域名WHOIS信息失败", e);
        }
    }

    /**
     * 获取域名Alexa排名（已停服，保留用于向后兼容）
     *
     * @param domain 域名
     * @return Alexa排名，如果不存在则返回0
     * @deprecated Alexa服务已停服，建议使用 {@link #getDomainRank(String)} 获取Tranco排名
     */
    @Deprecated
    public Integer getDomainAlexaRank(String domain) {
        // 如果缓存为空，加载域名Alexa排名信息
        if (domainAlexaMap.isEmpty()) {
            loadDomainAlexaRankInfo();
        }

        return domainAlexaMap.getOrDefault(domain, 0);
    }

    /**
     * 获取域名Tranco排名（推荐使用）
     *
     * @param domain 域名
     * @return Tranco排名，如果不存在则返回0
     */
    public Integer getDomainRank(String domain) {
        return trancoRankingManager.getDomainRank(domain);
    }

    /**
     * 获取域名流行度等级
     *
     * @param domain 域名
     * @return 流行度等级：1-极高，2-高，3-中等，4-低，5-极低，0-未知
     */
    public Integer getDomainPopularityLevel(String domain) {
        return trancoRankingManager.getDomainPopularityLevel(domain);
    }

    /**
     * 检查域名是否在排名列表中
     *
     * @param domain 域名
     * @return 如果域名在排名中返回true，否则返回false
     */
    public boolean isRankedDomain(String domain) {
        return trancoRankingManager.isRankedDomain(domain);
    }

    /**
     * 加载域名Alexa排名信息（已停服，保留用于向后兼容）
     */
    private void loadDomainAlexaRankInfo() {
        domainAlexaMap.clear();

        try (Connection conn = DatabaseConnectionManager.getConnection();
             Statement stmt = conn.createStatement()) {

            String sql = "SELECT domain, alexa_rank FROM tb_domain_alexa LIMIT 1000000";

            try (ResultSet rs = stmt.executeQuery(sql)) {
                while (rs.next()) {
                    domainAlexaMap.put(rs.getString(1), rs.getInt(2));
                }
            }

            log.info("域名Alexa排名信息加载成功，共加载 {} 条记录", domainAlexaMap.size());
        } catch (Exception e) {
            log.error("加载域名Alexa排名信息失败", e);
        }
    }

    /**
     * 刷新域名元数据缓存
     */
    public void refresh() {
        domainWhoisMap.clear();
        domainAlexaMap.clear();
        loadDomainWhoisInfo();
        loadDomainAlexaRankInfo();

        // 刷新Tranco排名数据
        if (trancoRankingManager != null) {
            trancoRankingManager.refresh();
        }
    }
}
