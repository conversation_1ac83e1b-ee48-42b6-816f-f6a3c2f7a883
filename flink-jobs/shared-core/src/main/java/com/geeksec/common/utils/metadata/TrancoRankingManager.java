package com.geeksec.common.utils.metadata;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.extern.slf4j.Slf4j;

/**
 * Tranco域名排名管理器
 *
 * Tranco是一个研究导向的域名排名列表，整合了多个数据源，
 * 具有更好的抗操纵性和研究价值
 *
 * <AUTHOR>
 */
@Slf4j
public class TrancoRankingManager {

    /**
     * 单例实例
     */
    private static volatile TrancoRankingManager instance = null;

    /**
     * 域名排名映射 (域名 -> 排名)
     */
    private final Map<String, Integer> domainRankMap = new ConcurrentHashMap<>();

    /**
     * Tranco数据文件路径
     */
    private static final String TRANCO_DATA_FILE = "/top-1m.csv";

    /**
     * 默认排名值（未找到排名时返回）
     */
    private static final int DEFAULT_RANK = 0;

    /**
     * 流行度等级阈值常量
     */
    private static final int POPULARITY_LEVEL_1_THRESHOLD = 1000;
    private static final int POPULARITY_LEVEL_2_THRESHOLD = 10000;
    private static final int POPULARITY_LEVEL_3_THRESHOLD = 100000;
    private static final int POPULARITY_LEVEL_4_THRESHOLD = 500000;

    /**
     * 私有构造函数
     */
    private TrancoRankingManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return TrancoRankingManager实例
     */
    public static TrancoRankingManager getInstance() {
        if (instance == null) {
            synchronized (TrancoRankingManager.class) {
                if (instance == null) {
                    instance = new TrancoRankingManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化Tranco排名数据
     */
    private void initialize() {
        try {
            loadTrancoRankingData();
            log.info("Tranco域名排名管理器初始化成功，共加载 {} 条排名记录", domainRankMap.size());
        } catch (Exception e) {
            log.error("初始化Tranco域名排名管理器失败", e);
        }
    }

    /**
     * 加载Tranco排名数据
     */
    private void loadTrancoRankingData() throws IOException {
        domainRankMap.clear();

        try (InputStream inputStream = getClass().getResourceAsStream(TRANCO_DATA_FILE)) {
            if (inputStream == null) {
                throw new IOException("无法找到Tranco数据文件: " + TRANCO_DATA_FILE);
            }

            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

                String line;
                int lineCount = 0;

                while ((line = reader.readLine()) != null) {
                    lineCount++;

                    // 跳过空行
                    if (line.trim().isEmpty()) {
                        continue;
                    }

                    try {
                        // 解析CSV格式: 排名,域名
                        String[] parts = line.split(",", 2);
                        if (parts.length == 2) {
                            int rank = Integer.parseInt(parts[0].trim());
                            String domain = parts[1].trim();

                            if (!domain.isEmpty()) {
                                domainRankMap.put(domain, rank);
                            }
                        }
                    } catch (NumberFormatException e) {
                        log.warn("解析Tranco数据第{}行失败: {}", lineCount, line);
                    }
                }

                log.info("成功加载Tranco排名数据，处理了 {} 行，有效记录 {} 条",
                        lineCount, domainRankMap.size());
            }
        }
    }

    /**
     * 获取域名的Tranco排名
     *
     * @param domain 域名
     * @return 域名排名，如果不存在则返回0
     */
    public int getDomainRank(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return DEFAULT_RANK;
        }

        return domainRankMap.getOrDefault(domain.trim().toLowerCase(), DEFAULT_RANK);
    }

    /**
     * 检查域名是否在Tranco排名中
     *
     * @param domain 域名
     * @return 如果域名在排名中返回true，否则返回false
     */
    public boolean isRankedDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }

        return domainRankMap.containsKey(domain.trim().toLowerCase());
    }

    /**
     * 获取排名数据的总数量
     *
     * @return 排名数据总数
     */
    public int getTotalRankingCount() {
        return domainRankMap.size();
    }

    /**
     * 刷新排名数据缓存
     */
    public void refresh() {
        try {
            loadTrancoRankingData();
            log.info("Tranco排名数据刷新成功，共 {} 条记录", domainRankMap.size());
        } catch (Exception e) {
            log.error("刷新Tranco排名数据失败", e);
        }
    }

    /**
     * 获取域名流行度等级
     * 基于排名将域名分为不同的流行度等级
     *
     * @param domain 域名
     * @return 流行度等级：1-极高，2-高，3-中等，4-低，5-极低，0-未知
     */
    public int getDomainPopularityLevel(String domain) {
        int rank = getDomainRank(domain);

        if (rank == 0) {
            // 未知
            return 0;
        } else if (rank <= POPULARITY_LEVEL_1_THRESHOLD) {
            // 极高流行度
            return 1;
        } else if (rank <= POPULARITY_LEVEL_2_THRESHOLD) {
            // 高流行度
            return 2;
        } else if (rank <= POPULARITY_LEVEL_3_THRESHOLD) {
            // 中等流行度
            return 3;
        } else if (rank <= POPULARITY_LEVEL_4_THRESHOLD) {
            // 低流行度
            return 4;
        } else {
            // 极低流行度
            return 5;
        }
    }
}
