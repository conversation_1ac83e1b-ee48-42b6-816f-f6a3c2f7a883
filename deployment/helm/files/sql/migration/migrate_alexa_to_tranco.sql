-- 数据库迁移脚本：从Alexa排名迁移到Tranco排名
-- 执行前请备份相关数据表

USE nta;

-- 1. 为dim_domain表添加新字段
ALTER TABLE dim_domain 
ADD COLUMN domain_rank INT REPLACE COMMENT "域名排名(基于Tranco数据)" AFTER domain,
ADD COLUMN popularity_level TINYINT REPLACE COMMENT "流行度等级(1-5，1为最高)" AFTER domain_rank;

-- 2. 如果需要保留历史数据，可以将alexa_rank数据迁移到domain_rank
-- UPDATE dim_domain SET domain_rank = alexa_rank WHERE alexa_rank IS NOT NULL AND alexa_rank > 0;

-- 3. 删除alexa_rank字段（可选，建议先测试新系统稳定后再执行）
-- ALTER TABLE dim_domain DROP COLUMN alexa_rank;

-- 4. 如果存在tb_domain_alexa表，可以选择删除或重命名
-- DROP TABLE IF EXISTS tb_domain_alexa;
-- 或者重命名保留
-- RENAME TABLE tb_domain_alexa TO tb_domain_alexa_backup;

-- 5. 更新Nebula图数据库的DOMAIN标签结构（需要在Nebula控制台执行）
/*
在Nebula控制台执行以下命令：

USE nta;

-- 添加新属性
ALTER TAG DOMAIN ADD (domain_rank int, popularity_level int);

-- 如果需要删除alexa_rank属性（可选）
-- ALTER TAG DOMAIN DROP (alexa_rank);
*/

-- 6. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_domain_rank ON dim_domain(domain_rank);
CREATE INDEX IF NOT EXISTS idx_popularity_level ON dim_domain(popularity_level);

-- 迁移完成提示
SELECT 'Alexa排名到Tranco排名迁移脚本执行完成' AS migration_status;
SELECT '请检查应用程序是否正常工作，确认无误后可删除alexa_rank相关字段' AS next_steps;
